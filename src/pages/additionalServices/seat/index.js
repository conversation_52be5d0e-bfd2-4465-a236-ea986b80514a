/**
 * 座位选择管理类
 * 负责处理乘客选择座位的所有逻辑
 */
class SeatSelectionManager {
  constructor(options = {}) {
    this.passengers = options.passengers || [];
    this.selectedPassengerIndex = 0;
    this.seatPrices = options.seatPrices || { free: 0, paid: 200 };
    this.currency = options.currency || 'CNY';

    this.init();
  }

  /**
   * 初始化座位选择器
   */
  init() {
    this.renderPassengers();
    this.bindEvents();
    this.updateSeatDisplay();
  }

  /**
   * 渲染乘客列表
   */
  renderPassengers() {
    const $passengerList = $('.passenger-list');

    if (!$passengerList.length || !this.passengers.length) {
      return;
    }

    const passengerHtml = this.passengers
      .map((passenger, index) => {
        const isSelected = index === this.selectedPassengerIndex;
        const seatInfo = this.renderSeatInfo(passenger);

        return `
          <div class="passenger-item ${isSelected ? 'selected' : ''}" data-passenger-index="${index}">
            <div class="passenger-number">${index + 1}</div>
            <div class="passenger-content">
              <div class="passenger-info">
                <div class="passenger-name">${passenger.name}</div>
                ${this.renderPassengerDetails(passenger)}
              </div>
              <div class="seat-info">
                ${seatInfo}
              </div>
            </div>
          </div>
        `;
      })
      .join('');

    $passengerList.html(passengerHtml);
  }

  /**
   * 渲染乘客详细信息
   */
  renderPassengerDetails(passenger) {
    if (!passenger.badge && !passenger.id) {
      return '';
    }

    return `
      <div class="passenger-details">
        ${passenger.badge ? `<div class="passenger-badge">${passenger.badge}</div>` : ''}
        ${passenger.id ? `<div class="passenger-id">${passenger.id}</div>` : ''}
      </div>
    `;
  }

  /**
   * 渲染座位信息
   */
  renderSeatInfo(passenger) {
    if (!passenger.selectedSeat) {
      return '<div class="no-seat-selected">No seat selected</div>';
    }

    const price = this.getSeatPrice(passenger.selectedSeat);
    const priceDisplay = price > 0 ? `${this.currency} ${price}` : '';

    return `
      <div class="seat-display">
        <div class="seat-icon-small"></div>
        <div class="seat-number">${passenger.selectedSeat}</div>
      </div>
      ${priceDisplay ? `<div class="seat-price">${priceDisplay}</div>` : ''}
    `;
  }

  /**
   * 获取座位价格
   */
  getSeatPrice(seatNumber) {
    const $seat = $(`.seat[data-seat="${seatNumber}"]`);
    return $seat.hasClass('seat-paid') ? this.seatPrices.paid : this.seatPrices.free;
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 乘客选择事件
    $(document)
      .off('click.seatSelection', '.passenger-item')
      .on('click.seatSelection', '.passenger-item', e => {
        const passengerIndex = parseInt($(e.currentTarget).data('passenger-index'));
        this.selectPassenger(passengerIndex);
      });

    // 座位选择事件
    $(document)
      .off('click.seatSelection', '.seat')
      .on('click.seatSelection', '.seat', e => {
        const $seat = $(e.currentTarget);
        const seatNumber = $seat.data('seat');

        if (this.isSeatSelectable($seat)) {
          this.selectSeat(seatNumber);
        }
      });

    // 键盘导航支持
    $(document)
      .off('keydown.seatSelection', '.seat')
      .on('keydown.seatSelection', '.seat', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          $(e.currentTarget).click();
        }
      });
  }

  /**
   * 选择乘客
   */
  selectPassenger(passengerIndex) {
    if (passengerIndex < 0 || passengerIndex >= this.passengers.length) {
      return;
    }

    this.selectedPassengerIndex = passengerIndex;
    this.updatePassengerSelection();
    this.updateSeatDisplay();
  }

  /**
   * 选择座位
   */
  selectSeat(seatNumber) {
    const currentPassenger = this.passengers[this.selectedPassengerIndex];
    if (!currentPassenger) {
      return;
    }

    // 检查座位是否已被其他乘客选择
    const occupiedBy = this.findPassengerBySeat(seatNumber);
    let replacedPassenger = null;

    if (occupiedBy && occupiedBy !== currentPassenger) {
      // 记录被替换的乘客信息
      replacedPassenger = { ...occupiedBy };

      // 清除其他乘客的座位选择
      occupiedBy.selectedSeat = null;

      // 更新被清除座位的乘客显示
      const occupiedPassengerIndex = this.passengers.indexOf(occupiedBy);
      if (occupiedPassengerIndex !== -1) {
        this.updatePassengerDisplay(occupiedPassengerIndex);
      }
    }

    // 记录当前乘客之前的座位
    const previousSeat = currentPassenger.selectedSeat;

    // 清除当前乘客之前选择的座位
    if (previousSeat && previousSeat !== seatNumber) {
      this.clearSeatSelection(previousSeat);
    }

    // 设置新座位
    currentPassenger.selectedSeat = seatNumber;

    // 更新显示
    this.updateSeatDisplay();
    this.updatePassengerDisplay(this.selectedPassengerIndex);

    // 触发座位选择事件
    this.onSeatSelected(currentPassenger, seatNumber, {
      previousSeat: previousSeat,
      replacedPassenger: replacedPassenger,
    });
  }

  /**
   * 检查座位是否可选择
   */
  isSeatSelectable($seat) {
    return (
      !$seat.hasClass('seat-not-available') && !$seat.hasClass('seat-unreserved') && !$seat.hasClass('seat-emergency')
    );
  }

  /**
   * 清除座位选择状态
   */
  clearSeatSelection(seatNumber) {
    $(`.seat[data-seat="${seatNumber}"]`).removeClass('seat-selected');
  }

  /**
   * 根据座位号查找乘客
   */
  findPassengerBySeat(seatNumber) {
    return this.passengers.find(passenger => passenger.selectedSeat === seatNumber);
  }

  /**
   * 检查座位是否被占用
   */
  isSeatOccupied(seatNumber) {
    return this.passengers.some(passenger => passenger.selectedSeat === seatNumber);
  }

  /**
   * 获取座位占用情况统计
   */
  getSeatOccupancyStats() {
    const totalPassengers = this.passengers.length;
    const seatedPassengers = this.passengers.filter(p => p.selectedSeat).length;
    const occupiedSeats = new Set(this.passengers.map(p => p.selectedSeat).filter(Boolean));

    return {
      totalPassengers,
      seatedPassengers,
      unseatedPassengers: totalPassengers - seatedPassengers,
      occupiedSeatsCount: occupiedSeats.size,
      occupiedSeats: Array.from(occupiedSeats),
    };
  }

  /**
   * 更新座位地图显示
   */
  updateSeatDisplay() {
    // 清除所有选中状态
    $('.seat').removeClass('seat-selected');

    // 标记已选择的座位
    this.passengers.forEach(passenger => {
      if (passenger.selectedSeat) {
        $(`.seat[data-seat="${passenger.selectedSeat}"]`).addClass('seat-selected');
      }
    });
  }

  /**
   * 更新乘客选择状态
   */
  updatePassengerSelection() {
    $('.passenger-item').removeClass('selected');
    $(`.passenger-item[data-passenger-index="${this.selectedPassengerIndex}"]`).addClass('selected');
  }

  /**
   * 更新单个乘客显示
   */
  updatePassengerDisplay(passengerIndex) {
    const passenger = this.passengers[passengerIndex];
    const $passengerItem = $(`.passenger-item[data-passenger-index="${passengerIndex}"]`);
    const $seatInfo = $passengerItem.find('.seat-info');

    $seatInfo.html(this.renderSeatInfo(passenger));
  }

  /**
   * 座位选择回调
   */
  onSeatSelected(passenger, seatNumber, additionalInfo = {}) {
    $(document).trigger('seatSelected', {
      passenger: passenger,
      seatNumber: seatNumber,
      price: this.getSeatPrice(seatNumber),
      previousSeat: additionalInfo.previousSeat,
      replacedPassenger: additionalInfo.replacedPassenger,
    });
  }

  /**
   * 获取所有已选择的座位信息
   */
  getSelectedSeats() {
    return this.passengers
      .filter(passenger => passenger.selectedSeat)
      .map(passenger => ({
        passenger: passenger,
        seat: passenger.selectedSeat,
        price: this.getSeatPrice(passenger.selectedSeat),
      }));
  }

  /**
   * 获取总价格
   */
  getTotalPrice() {
    return this.getSelectedSeats().reduce((total, selection) => total + selection.price, 0);
  }

  /**
   * 清除所有座位选择
   */
  clearAllSelections() {
    this.passengers.forEach(passenger => {
      passenger.selectedSeat = null;
    });
    this.updateSeatDisplay();
    this.renderPassengers();
  }

  /**
   * 验证是否所有乘客都已选择座位
   */
  isAllPassengersSeated() {
    return this.passengers.every(passenger => passenger.selectedSeat);
  }

  /**
   * 获取未选择座位的乘客
   */
  getUnseatedPassengers() {
    return this.passengers.filter(passenger => !passenger.selectedSeat);
  }
}

// 座位选择初始化
$(document).ready(function () {
  // 示例乘客数据
  const defaultPassengerData = [
    {
      name: 'ZHANG/SAN',
      badge: '终身白金卡',
      id: '1230000213',
      selectedSeat: '10E',
    },
    {
      name: 'LI/SAN',
      selectedSeat: '10K',
    },
    {
      name: 'WANG/SAN',
    },
    {
      name: 'ZHAO/SAN',
    },
  ];

  // 延迟初始化确保 DOM 完全加载
  setTimeout(() => {
    // 使用外部数据或默认数据
    if (typeof window.passengerData === 'undefined') {
      window.passengerData = defaultPassengerData;
    }

    // 初始化座位选择管理器
    if (window.passengerData && window.passengerData.length > 0) {
      window.seatSelectionManager = new SeatSelectionManager({
        passengers: window.passengerData,
        seatPrices: { free: 0, paid: 200 },
        currency: 'CNY',
      });

      // 监听座位选择事件
      $(document).on('seatSelected', function (event, data) {
        // 可以在这里添加自定义处理逻辑
        if (data.replacedPassenger) {
          console.log(
            `Seat ${data.seatNumber} reassigned from ${data.replacedPassenger.name} to ${data.passenger.name}`
          );
        } else {
          console.log(`${data.passenger.name} selected seat ${data.seatNumber}`);
        }

        if (data.previousSeat && data.previousSeat !== data.seatNumber) {
          console.log(`${data.passenger.name} moved from seat ${data.previousSeat} to ${data.seatNumber}`);
        }
      });
    } else {
      $('.passenger-list').html('<div class="no-passengers">No passengers found</div>');
    }
  }, 300);
});

// 外部接口：设置乘客数据
window.setSeatPassengerData = function (passengerData) {
  window.passengerData = passengerData;

  if (window.seatSelectionManager) {
    window.seatSelectionManager.passengers = passengerData;
    window.seatSelectionManager.selectedPassengerIndex = 0;
    window.seatSelectionManager.renderPassengers();
    window.seatSelectionManager.updateSeatDisplay();
  } else {
    // 重新初始化
    window.seatSelectionManager = new SeatSelectionManager({
      passengers: passengerData,
      seatPrices: { free: 0, paid: 200 },
      currency: 'CNY',
    });
  }
};

// 外部接口：获取选择结果
window.getSeatSelectionResult = function () {
  if (window.seatSelectionManager) {
    return {
      selectedSeats: window.seatSelectionManager.getSelectedSeats(),
      totalPrice: window.seatSelectionManager.getTotalPrice(),
      isAllSeated: window.seatSelectionManager.isAllPassengersSeated(),
      unseatedPassengers: window.seatSelectionManager.getUnseatedPassengers(),
    };
  }
  return null;
};
