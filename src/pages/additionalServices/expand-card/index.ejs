<%
  const _contentId = typeof contentId !== 'undefined' ? contentId : '';
  const _flightData = typeof flightData !== 'undefined' ? flightData : {};
  const _statusInfo = typeof statusInfo !== 'undefined' ? statusInfo : { icon: 'check.svg', text: 'Selected', class: 'status-selected' };

%>

<div class="expand-card">
  <div class="flight-info-card">
    <div class="card-header"></div>
    <div class="card-content">
      <div class="flight-details">
        <div class="journey-badge">Journey <%= _flightData.journeyNumber || '' %></div>
        <div class="route-info">
          <span class="departure-city"><%= _flightData.departureCity || '' %></span>
          <div class="route-arrow">
            <img src="../../images/additionalServices/way.svg" alt="" />
          </div>
          <span class="arrival-city"><%= _flightData.arrivalCity || '' %></span>
        </div>
        <div class="flight-date"><%= _flightData.flightDate || '' %></div>
        <div class="flight-time">
          <span class="departure-time"><%= _flightData.departureTime || '' %></span>
          <span class="time-separator">-</span>
          <span class="arrival-time"><%= _flightData.arrivalTime || '' %></span>
        </div>
        <div class="changes-info">
          <span class="changes-text">Changes/Cancellations</span>
          <div
            class="tips"
            role="tooltip"
            tabindex="0"
            data-tooltip="Flight Change Policy"
            data-tippy-content="<%= _flightData.changesInfo || '' %>"
            data-tippy-allowHTML="true">
            <div class="info-icon">
              <img src="../../images/additionalServices/question.svg" alt="" />
            </div>
          </div>
        </div>
        <div class="status-info <%= _statusInfo.class %>">
          <div class="status-icon">
            <img src="../../images/additionalServices/<%= _statusInfo.icon %>" alt="" />
          </div>
          <span class="status-text"><%= _statusInfo.text %></span>
        </div>
      </div>
      <div class="expand-control">
        <span class="expand-text">expand</span>
        <div class="expand-icon">
          <img src="../../images/additionalServices/arrow.svg" alt="" />
        </div>
      </div>
    </div>

    <div class="expand-content" style="display: none" data-source="<%= _contentId %>"></div>
  </div>
</div>
